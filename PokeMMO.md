### POKEMMO
这个是一个关于POKEMMO的孵蛋说明 首先是对于宝可梦来说有很多相关属性 例如宝可梦等级 姓名 性别 蛋组 性格 个体值 是否闪光等  但是对于孵蛋来说的话只需要关注一些相关的内容即可例如孵蛋的宝可梦的蛋组如果不是无性别组的 就需要是相同蛋组并且是一公一母才可以进行孵蛋 然后孵蛋后的父母双方就没有了 只会得到一个母方的后代，在孵蛋的时候可以指定子代的性别 但是需要花费游戏币进行指定。
在游戏中 每个宝可梦有六个个体 每个个体值的范围是0-31 其中一个个体值为31称作V 有几个个体值为31 就叫几V 然后如果在孵蛋的时候用户想要将两个亲代的一些个体值遗传给子代的话 就需要使用道具 锁定个体值的道具一共有6种 分别对应六个个体值属性(HP 攻击 防御 特攻 特防 速度) eg1.现在有一个母体的个体值分别是 HP：31 攻击：20 防御：20 特攻：20 特防：20 速度：20 然后父体的个体值是 HP：25 攻击：25 防御：25 特攻：25 特防：25 速度：31 然后我给母体带上一个锁定HP的道具 然后给父体带一个锁定速度的道具那么在不锁定子代的性别的情况下 子代的个体值中HP和速度就会遗传母体的HP的31和父体的速度31 然后其他的几项就是在母体和父体的范围中 子代具体的个体就是HP：31 攻击：20 ~ 25 防御：20 ~ 25 特攻：20 ~ 25 特防：20 ~ 25 速度：31 eg2.现在有一个母体的个体值分别是 HP：31 攻击：20 防御：20 特攻：20 特防：31 速度：20 然后父体的个体值是 HP：25 攻击：25 防御：25 特攻：25 特防：31 速度：31 然后我给母体带上一个锁定HP的道具 然后给父体带一个锁定速度的道具那么在不锁定子代的性别的情况下 子代的个体值中HP和速度就会遗传母体的HP的31和父体的速度31 然后其他的几项就是在母体和父体的范围中 子代具体的个体就是HP：31 攻击：20 ~ 25 防御：20 ~ 25 特攻：20 ~ 25 特防：31 速度：31。然后以此类推 需要得到高个体的子代就需要多次进行孵蛋 如果需要得到一个5v的子代 就需要两个不完全相同的4v进行孵蛋 得到一个4v 就需要两个3v 得到3v就需要两个2v 得到2v就需要两个1v。
对于子代的性格用户可以使用不变之石对母体或者父体进行锁定 然后将性格遗传给子代 如果不锁定性格 那么子代的性格就是随机的 eg1. 现在有一个母体的个体值分别是 HP：31 攻击：20 防御：31 特攻：31 特防：31 速度：31 然后性格是：悠闲 然后父体的个体值是 HP：31 攻击：25 防御：31 特攻：25 特防：31 速度：31 性格是：内敛 然后给母体带上锁定特攻的道具 然后给父体带上不变之石 那么子代就是 HP：31 攻击：20 ~ 25 防御：31 特攻：31 特防：31 速度：31 然后子代的性格就是内敛。  eg2. 现在有一个母体的个体值分别是 HP：31 攻击：20 防御：31 特攻：15 特防：31 速度：31 然后性格是：固执 然后父体的个体值是 HP：31 攻击：31 防御：31 特攻：25 特防：31 速度：31 性格是：内敛 然后给母体带上不变之石 然后给父体带上锁定攻击的道具 那么子代就是 HP：31 攻击：31 防御：31 特攻：15 ~ 25 特防：31 速度：31 然后子代的性格就是固执。
对于锁定宝可梦的性别 如果该宝可梦的公母比是1：1的话就是5000游戏内的金币 如果 公母比是1：3 那么锁定公就是9000 锁定母就是5000， 如果公母比是7：1 那么锁定公就是21000 锁定母也是21000
然后对于一些宝可梦来说 有些技能是无法通过直接升级和技能学习机得到 必须通过遗传才能学到
现在我想要做一个宝可梦的仓库管理系统 然后附加一个孵蛋计算器的功能 这个仓库管理系统可以用于管理我拥有的宝可梦 相同的宝可梦我可能会有很多只 当然这个仓库可以无上限的容纳我添加的宝可梦 然后我可以设置每个宝可梦的个体值和价格性别 还有蛋组 技能等信息 然后我需要你在使用孵蛋计算器的时候从仓库中选择一些宝可梦来计算要得到对应的宝可梦需要花费的最少游戏币 我会在孵蛋计算器给你一个需要得到的结果 比如果 我仓库中有某一个蛋组的宝可梦很多只 
1.{
    HP: 20,
    攻击: 31,
    防御: 21,
    特攻: 15,
    特防: 21,
    速度: 31,
    价格: 45000,
    性别: 公，
    蛋组: 陆上组
},
2.{
    HP: 20,
    攻击: 31,
    防御: 31,
    特攻: 15,
    特防: 21,
    速度: 21,
    价格: 42000,
    性别: 公，
    蛋组: 植物组
},
3.{
    HP: 21,
    攻击: 0,
    防御: 31,
    特攻: 15,
    特防: 21,
    速度: 31,
    价格: 44000,
    性别: 母，
    蛋组: 陆上组
},
4.{
    HP: 31,
    攻击: 0,
    防御: 31,
    特攻: 15,
    特防: 21,
    速度: 31,
    价格: 84000,
    性别: 母，
    蛋组: 陆上组
}

然后我想要得到一个4v的陆上组公的某宝可梦 然后我就需要选择上面的第一只和第三只宝可梦进行第一次孵蛋 然后锁定性别为公 然后给第一只宝可梦带上锁定攻击的道具 然后给第三只宝可梦带上锁定防御的道具 这样我就要花费 第一只宝可梦的价格（45000） + 第三只宝可梦的价格（44000） + 锁定性别的价格（5000） + 两个锁定个体值的道具（20000）= 114000
{
    HP: 20~21,
    攻击：31,
    防御: 31,
    特攻: 15,
    特防: 21,
    速度: 31,
    价格: 114000,
    性别: 公,
    蛋组: 陆上组
}

然后我将得到的这个3v宝可梦和仓库中的第四只宝可梦进行孵蛋 给这个3v宝可梦带上锁定攻击的道具 然后给第四只宝可梦带上锁定HP的道具 然后锁定孵蛋得到的宝可梦性别为公 这样我就要花费 上面得到的3v宝可梦的价格(114000) + 第四只宝可梦的价格(84000) + 锁定性别的价格(5000) + 锁定两个个体值的道具(20000) = 223000
{
    HP: 31,
    攻击：31,
    防御: 31,
    特攻: 15,
    特防: 21,
    速度: 31,
    价格: 223000,
    性别: 公,
    蛋组: 陆上组
}
我需要的就是将这个宝可梦仓库和孵蛋系统实现 然后可以添加一个筛选的功能 筛选蛋组等
关于宝可梦的蛋组有几个细节
- 百变怪是单独的一个蛋组 它可以和任何宝可梦除了百变怪和未发现蛋组以外的宝可梦进行孵蛋并且没有性别一说 百变怪可以和无性别蛋组进行孵蛋 孵出来的子代就是该无性别蛋组的蛋 
- 无性别蛋组只能和他相同的宝可梦进行孵蛋 当然也可以和百变怪进行孵蛋 得到的子代同样是该宝可梦的
- 未发现蛋组 这个蛋组中的宝可梦是无法进行孵蛋的 但是有些特殊的未发现蛋组的宝可梦 进化过后就变成了可以孵蛋的蛋组 
- 普通可以直接公母孵蛋的蛋组: 怪兽组 水1组 虫组 飞行组 陆上组 妖精组 植物组 人形组 水2组 水3组 矿物组 不定形组 龙组。这些蛋组都是可以正常的进行公母孵蛋的蛋组 但是只能同蛋组间进行孵蛋


项目需要用到数据库 保证我后期可以添加宝可梦进入 然后项目中展示的宝可梦图标是GIF图片 所以需要用到对应的加载GIF图片的方法 
本地数据库的用户名是root 密码是XZJ020601
